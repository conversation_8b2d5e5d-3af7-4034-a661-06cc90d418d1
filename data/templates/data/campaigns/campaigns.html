
{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load humanize %}
{% load i18n %}
{% load hosts %}

{% block content %}
<div class="d-flex d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    
    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button id='view-sync-items' type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button"
                style="width: 130px"
                {% if studio_type == 'emails' %}
                    hx-get="{% url 'load_create_drawer' %}"
                    hx-vals='{"drawer_type":"sync_emails", "view_id": "{{view_filter.view.id}}"}'
                {% else %}
                    hx-get="{% url 'campaignscale_settings' %}" 
                    hx-vals='{"drawer_type":"view-syncs"}'
                {% endif%}
                hx-trigger="click"
                hx-target="#manage-campaign-drawer"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"></path>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"></path>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>

            <button type="button" class="btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-content-wizard-button {% if view_filter.view_type == 'calendar' %}d-none{% endif %}"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#manage-drawer-campaigns"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download
                    {% endif %}
                </span>
            </button>
        </div>

        <div class="me-2">
            <div data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
                <button class="w-125px align-items-center d-flex btn btn-primary py-1 rounded-1 content_review_button" type="button"
                    hx-get="{% host_url 'load_create_drawer' host 'app' %}"
                    {% if studio_type == 'emails' %}
                        hx-vals='{"drawer_type":"send_email", "view_id": "{{view_id}}"}'
                    {% else %}  
                        hx-vals='{"drawer_type":"sns", "view_id": "{{view_id}}"}'
                    {% endif %}
                    hx-target="#content_review_wizard"
                    hx-indicator=".loading-drawer-spinner,.content_form"
                    hx-trigger="click"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                            <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                            <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                        </svg>
                    </span>
                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>
<div class="d-flex flex-column flex-lg-row">
    <div class="w-100 pb-5 px-10">
        {% include 'data/campaigns/view-menu.html' with view=current_view studio_type='' enable_create=True enable_sync=True %}
        
        {% if current_view.viewfilter_set.first.view_type == 'calendar' %}
            <div class="">
                <div class="text-muted me-2">
                    {% if LANGUAGE_CODE == 'ja'%}
                    タイムゾーン:
                    {% else %}
                    Timezone:
                    {% endif %}
                    <span>
                        {% if workspace.timezone %}
                        {{workspace.timezone}}
                        {% else %}
                        UTC
                        {% endif %}
                        
                    </span>
                </div>

                <div id='calendar'></div>
            </div>

            {% if post_id %}
            <button 
                class="btn fw-bolder text-hover-primary content_review_button px-0 d-none"
                id="content_review_button_{{post_id}}"
                hx-indicator=".loading-drawer-spinner,.content_form"
                {% if post.platform == 'wordpress' or post.channel.integration.slug == 'wordpress' %}
                hx-get="{% host_url 'get_review_content_drawer' post_id host 'app' %}"
                {% elif post.platform == 'sendgrid' %}
                hx-get="{% host_url 'get_review_emails_drawer' post_id host 'app' %}"
                {% else %}
                hx-get="{% host_url 'get_review_social_post_drawer' post_id host 'app' %}"
                {% endif %}
                hx-vals='{"view_id": "{{view_id}}"}'
                hx-target="#content_review_wizard"
                hx-trigger="click"
                onclick="document.getElementById('content_review_wizard').replaceChildren()"
                >
                {{post.body|truncatechars:50}}
            </button>
            <script>
                $(document).ready(function() {
                    $('#content_review_button_{{post_id}}').click()
                })
            </script>
            {% endif %}

        {% else %}
            {% include 'data/campaigns/partial-social-media.html' %}
            
        {% endif%}
    </div>
</div>


<style>
    #images, #event-images{
        width: 90%;
        position: relative;
        margin: auto;
        display: flex;
        justify-content: space-evenly;
        gap: 20px;
        flex-wrap: wrap;
    }
    #images figure{
        width: 45%;
    }
    #event-images figure{
        width: 100%;
    }

    .new-post img{
        width: 100%;
        height:100%;
        object-fit: contain;

    }
    figcaption{
        text-align: center;
        font-size: 14px;
        margin-top: 0.5vmin;
    }
    #event-images img {
        width:100%;
    }

</style>


<style>
    .post-item:hover .post-selection{
        border-width: 1px !important;
    }
</style>

{% endblock %}

{% block js %}
{% include 'data/javascript/studioJS.html' %}
{% endblock %}
