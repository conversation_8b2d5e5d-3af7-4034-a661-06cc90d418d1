<style>
    /* SCOPED CSS: All rules apply only within .sanka-table containers */

    .sanka-table table {
        width: auto;
    }

    .sanka-table td,
    .sanka-table th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sanka-table td.z-index-3 {
        z-index: 15!important;
    }

    /* Ensure dropdown menus appear above sticky headers within sanka-table */
    .sanka-table .dropdown-menu.show {
        z-index: 30!important;
    }

    .sanka-table .dropdown-menu {
        z-index: 30!important;
    }

    .sanka-table .fa-arrow-down{
        transform: rotate(0deg);
        transition: transform 0.2s linear;
    }

    .sanka-table .fa-arrow-down.open{
        transform: rotate(-90deg);
        transition: transform 0.2s linear;
    }

    .sanka-table .svg-container svg {
        max-height: 100px;
    }

    /* FIXED: Scoped DataTables ScrollHead with proper z-index hierarchy */
    .sanka-table .dataTables_scrollHead {
        position: sticky !important;
        top: 0 !important;
        z-index: 20 !important; /* Higher than sticky columns */
        background-color: white !important;
    }

    /* Target the actual scrollable header container */
    .sanka-table .dataTables_scrollHeadInner {
        border-bottom: 1px solid #dee2e6 !important;
        background-color: white !important;
        z-index: 20 !important;
    }

    /* Target the table within the scroll head */
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner table {
        border-bottom: 1px solid #dee2e6 !important;
        margin-bottom: 0 !important;
    }

    /* Target header row specifically */
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner table thead tr {
        border-bottom: 1px solid #dee2e6 !important;
        background-color: white !important;
    }

    /* Target individual header cells with enhanced specificity */
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner table thead tr th,
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner table thead tr td {
        border-bottom: 1px solid #dee2e6 !important;
        background-color: white !important;
        position: relative !important;
        z-index: 20 !important;
    }

    /* FIXED: Sticky header cells for fixed columns with highest z-index */
    .sanka-table .dataTables_scrollHead th:nth-child(1),
    .sanka-table .dataTables_scrollHead td:nth-child(1),
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner th:nth-child(1),
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner td:nth-child(1) {
        position: sticky !important;
        left: 0 !important;
        z-index: 25 !important; /* Highest z-index for header intersection */
        background-color: white !important;
    }

    .sanka-table .dataTables_scrollHead th:nth-child(2),
    .sanka-table .dataTables_scrollHead td:nth-child(2),
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner th:nth-child(2),
    .sanka-table .dataTables_scrollHead .dataTables_scrollHeadInner td:nth-child(2) {
        position: sticky !important;
        left: 60px !important;
        z-index: 24 !important; /* Second highest z-index for header intersection */
        background-color: white !important;
        border-right: 2px solid #dee2e6 !important;
        border-bottom: 2px solid #dee2e6 !important;
    }

    /* Add bottom border to the entire scroll head container as fallback */
    .sanka-table .dataTables_scrollHead::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 22;
        pointer-events: none;
    }
    /* SCOPED: DataTables FixedColumns styles with proper z-index hierarchy */
    /* First fixed column (checkbox) - Header cells have highest z-index */
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > th.dtfc-fixed-left:nth-child(1),
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > td.dtfc-fixed-left:nth-child(1) {
        position: sticky !important;
        left: 0 !important;
        z-index: 25 !important; /* Highest for header intersection */
        background-color: white !important;
        min-width: 60px !important;
        width: 60px !important;
    }

    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > th.dtfc-fixed-left:nth-child(1),
    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > td.dtfc-fixed-left:nth-child(1) {
        position: sticky !important;
        left: 0 !important;
        z-index: 15 !important; /* Lower for body cells */
        background-color: white !important;
        min-width: 60px !important;
        width: 60px !important;
    }

    /* Second fixed column (ID column) - Header cells have highest z-index */
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > th.dtfc-fixed-left:nth-child(2),
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > td.dtfc-fixed-left:nth-child(2) {
        position: sticky !important;
        left: 60px !important;
        z-index: 24 !important; /* Second highest for header intersection */
        background-color: white !important;
        border-right: 2px solid #dee2e6 !important;
        border-bottom: 2px solid #dee2e6 !important;
        min-width: 100px !important;
    }

    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > th.dtfc-fixed-left:nth-child(2),
    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > td.dtfc-fixed-left:nth-child(2) {
        position: sticky !important;
        left: 60px !important;
        z-index: 14 !important; /* Lower for body cells */
        background-color: white !important;
        border-right: 2px solid #dee2e6 !important;
        min-width: 100px !important;
    }

    /* Ensure all fixed columns have proper background and borders */
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > th.dtfc-fixed-left,
    .sanka-table table.dataTable.fixedColumns-left > thead > tr > td.dtfc-fixed-left {
        background-color: white !important;
        border-right: 1px solid #dee2e6 !important;
        border-bottom: 2px solid #dee2e6 !important;
    }

    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > th.dtfc-fixed-left,
    .sanka-table table.dataTable.fixedColumns-left > tbody > tr > td.dtfc-fixed-left {
        background-color: white !important;
        border-right: 1px solid #dee2e6 !important;
    }

    /* FALLBACK: Only apply manual sticky when DataTables FixedColumns is not working */
    /* These rules only apply when .dtfc-fixed-left class is NOT present */
    .sanka-table table.dataTable:not(.fixedColumns-left) thead th:nth-child(1),
    .sanka-table table.dataTable:not(.fixedColumns-left) thead td:nth-child(1),
    .sanka-table #contacts-table:not(.fixedColumns-left) thead th:nth-child(1),
    .sanka-table #contacts-table:not(.fixedColumns-left) thead td:nth-child(1) {
        position: sticky !important;
        left: 0 !important;
        z-index: 25 !important;
        background-color: white !important;
        min-width: 60px !important;
        width: 60px !important;
    }

    .sanka-table table.dataTable:not(.fixedColumns-left) tbody th:nth-child(1),
    .sanka-table table.dataTable:not(.fixedColumns-left) tbody td:nth-child(1),
    .sanka-table #contacts-table:not(.fixedColumns-left) tbody th:nth-child(1),
    .sanka-table #contacts-table:not(.fixedColumns-left) tbody td:nth-child(1) {
        position: sticky !important;
        left: 0 !important;
        z-index: 15 !important;
        background-color: white !important;
        min-width: 60px !important;
        width: 60px !important;
    }

    .sanka-table table.dataTable:not(.fixedColumns-left) thead th:nth-child(2),
    .sanka-table table.dataTable:not(.fixedColumns-left) thead td:nth-child(2),
    .sanka-table #contacts-table:not(.fixedColumns-left) thead th:nth-child(2),
    .sanka-table #contacts-table:not(.fixedColumns-left) thead td:nth-child(2) {
        position: sticky !important;
        left: 60px !important;
        z-index: 24 !important;
        background-color: white !important;
        border-right: 2px solid #dee2e6 !important;
        min-width: 100px !important;
    }

    .sanka-table table.dataTable:not(.fixedColumns-left) tbody th:nth-child(2),
    .sanka-table table.dataTable:not(.fixedColumns-left) tbody td:nth-child(2),
    .sanka-table #contacts-table:not(.fixedColumns-left) tbody th:nth-child(2),
    .sanka-table #contacts-table:not(.fixedColumns-left) tbody td:nth-child(2) {
        position: sticky !important;
        left: 60px !important;
        z-index: 14 !important;
        background-color: white !important;
        border-right: 2px solid #dee2e6 !important;
        min-width: 100px !important;
    }

    /* Let DataTables FixedColumns handle positioning - only provide styling support */
    .sanka-table .dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table th:nth-child(1),
    .sanka-table .dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table td:nth-child(1),
    .sanka-table .dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table th:nth-child(2),
    .sanka-table .dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table td:nth-child(2) {
        background-color: white !important;
    }

    /* Support DataTables FixedColumns with proper styling */
    .sanka-table table.dataTable.fixedColumns-left th:nth-child(1),
    .sanka-table table.dataTable.fixedColumns-left td:nth-child(1),
    .sanka-table table.dataTable.fixedColumns-left th:nth-child(2),
    .sanka-table table.dataTable.fixedColumns-left td:nth-child(2) {
        background-color: white !important;
    }

    /* SCOPED: DataTables scroll container management for sticky columns */
    .sanka-table .dataTables_wrapper {
        position: relative !important;
        overflow: visible !important;
        /* Enable hardware acceleration for better sticky performance */
        will-change: scroll-position;
    }

    .sanka-table .dataTables_wrapper .dataTables_scroll {
        position: relative !important;
        overflow: visible !important;
    }

    /* Configure scroll body for proper sticky column behavior */
    .sanka-table .dataTables_wrapper .dataTables_scroll .dataTables_scrollBody {
        overflow-x: auto !important;
        overflow-y: auto !important;
        position: relative !important;
        /* Create stacking context for sticky positioning */
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    /* Configure scroll head for proper sticky behavior */
    .sanka-table .dataTables_wrapper .dataTables_scrollHead {
        position: sticky !important;
        top: 0 !important;
        z-index: 20 !important; /* Consistent with header z-index */
        overflow: visible !important;
        /* Ensure header stays above content */
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    .sanka-table .dataTables_wrapper .dataTables_scrollHead .dataTables_scrollHeadInner {
        overflow: visible !important;
        position: relative !important;
    }

    /* Ensure table wrapper configuration supports sticky positioning */
    .sanka-table #contacts-table_wrapper {
        overflow: visible !important;
        position: relative !important;
        will-change: scroll-position;
    }

    /* Configure table elements for proper sticky behavior */
    .sanka-table .dataTables_scrollHead,
    .sanka-table .dataTables_scrollBody,
    .sanka-table .dataTables_scrollHeadInner {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }

    /* Ensure tables within scroll containers support sticky columns */
    .sanka-table .dataTables_scrollBody table,
    .sanka-table .dataTables_scrollHead table {
        position: relative !important;
        border-collapse: separate !important;
    }
    
    /* SCOPED: Fix for nav/dropdown z-index issues within sanka-table */
    .sanka-table .nav-tabs,
    .sanka-table .dropdown-menu,
    .sanka-table .dropdown-menu.show,
    .sanka-table .btn-group .dropdown-menu,
    .sanka-table ul.dropdown-menu {
        z-index: 35 !important; /* Above sticky elements but below global dropdowns */
        position: relative !important;
    }

    /* Specific fix for dropdowns with transform3d within sanka-table */
    .sanka-table .dropdown-menu[style*="transform"] {
        z-index: 36 !important;
    }

    /* Fix for dropdown container within sanka-table */
    .sanka-table .dropdown,
    .sanka-table .btn-group {
        z-index: 35 !important;
        position: relative !important;
    }

    /* Fix tab pane z-index to work with sticky columns within sanka-table */
    .sanka-table .tab-pane {
        z-index: 1 !important;
    }
    
    /* SCOPED: Reduce sticky column z-index when dropdowns are active */
    .sanka-table .dropdown.show ~ * #contacts-table th:nth-child(1),
    .sanka-table .dropdown.show ~ * #contacts-table td:nth-child(1),
    .sanka-table .dropdown.show ~ * #contacts-table th:nth-child(2),
    .sanka-table .dropdown.show ~ * #contacts-table td:nth-child(2) {
        z-index: 1 !important;
    }

    /* SCOPED: Universal fix for ID column border - ensures border is always visible */
    .sanka-table #contacts-table th:nth-child(2),
    .sanka-table #contacts-table td:nth-child(2),
    .sanka-table #contacts-table th.dtfc-fixed-left:nth-child(2),
    .sanka-table #contacts-table td.dtfc-fixed-left:nth-child(2),
    .sanka-table table.dataTable th:nth-child(2),
    .sanka-table table.dataTable td:nth-child(2),
    .sanka-table table.dataTable th.dtfc-fixed-left:nth-child(2),
    .sanka-table table.dataTable td.dtfc-fixed-left:nth-child(2) {
        border-right: 1px solid #dee2e6 !important;
    }

    /* SCOPED: Ensure proper layering during horizontal scroll */
    .sanka-table #contacts-table th:nth-child(2):after,
    .sanka-table #contacts-table td:nth-child(2):after {
        content: '';
        position: absolute;
        top: 0;
        right: -1px;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
        z-index: 1;
        pointer-events: none;
    }

    /* SCOPED: Remove bottom border from last row in DataTables to clean up pagination area */
    .sanka-table table.dataTable tbody tr:last-child,
    .sanka-table table.dataTable tbody tr:last-child td,
    .sanka-table table.dataTable tbody tr:last-child th,
    .sanka-table #contacts-table tbody tr:last-child,
    .sanka-table #contacts-table tbody tr:last-child td,
    .sanka-table #contacts-table tbody tr:last-child th,
    .sanka-table .table-content tbody tr:last-child,
    .sanka-table .table-content tbody tr:last-child td,
    .sanka-table .table-content tbody tr:last-child th {
        border-bottom: none !important;
        border-bottom-width: 0 !important;
        border-bottom-style: none !important;
    }

    /* SCOPED: Also remove any dotted or dashed borders specifically */
    .sanka-table table.dataTable tbody tr:last-child td,
    .sanka-table table.dataTable tbody tr:last-child th,
    .sanka-table #contacts-table tbody tr:last-child td,
    .sanka-table #contacts-table tbody tr:last-child th,
    .sanka-table .table-content tbody tr:last-child td,
    .sanka-table .table-content tbody tr:last-child th {
        border-bottom: 0 !important;
        box-shadow: none !important;
    }

    /* SCOPED: Remove border-top from first data row to prevent header border conflict */
    .sanka-table table.dataTable tbody tr:first-child,
    .sanka-table table.dataTable tbody tr:first-child td,
    .sanka-table table.dataTable tbody tr:first-child th,
    .sanka-table #contacts-table tbody tr:first-child,
    .sanka-table #contacts-table tbody tr:first-child td,
    .sanka-table #contacts-table tbody tr:first-child th,
    .sanka-table .table-content tbody tr:first-child,
    .sanka-table .table-content tbody tr:first-child td,
    .sanka-table .table-content tbody tr:first-child th,
    .sanka-table .dataTables_scrollBody table tbody tr:first-child,
    .sanka-table .dataTables_scrollBody table tbody tr:first-child td,
    .sanka-table .dataTables_scrollBody table tbody tr:first-child th {
        border-top: none !important;
        border-top-width: 0 !important;
        border-top-style: none !important;
    }

    /* SCOPED: Enhanced sticky header styling for all DataTables */
    /* Target all possible header containers */
    .sanka-table .dataTables_wrapper,
    .sanka-table .dataTables_wrapper .dataTables_scroll,
    .sanka-table .dataTables_wrapper .dataTables_scrollHead,
    .sanka-table table.dataTable,
    .sanka-table #contacts-table_wrapper,
    .sanka-table .table-responsive {
        position: relative !important;
    }

    /* Multiple targeting approaches for header borders */
    .sanka-table table.dataTable thead tr,
    .sanka-table #contacts-table thead tr,
    .sanka-table .table-content thead tr,
    .sanka-table .dataTables_scrollHead thead tr,
    .sanka-table .dataTables_scrollHeadInner thead tr {
        background-color: white !important;
        position: relative !important;
    }

    /* Target header cells with maximum specificity */
    .sanka-table table.dataTable thead tr th,
    .sanka-table table.dataTable thead tr td,
    .sanka-table #contacts-table thead tr th,
    .sanka-table #contacts-table thead tr td,
    .sanka-table .table-content thead tr th,
    .sanka-table .table-content thead tr td,
    .sanka-table .dataTables_scrollHead thead tr th,
    .sanka-table .dataTables_scrollHead thead tr td,
    .sanka-table .dataTables_scrollHeadInner thead tr th,
    .sanka-table .dataTables_scrollHeadInner thead tr td {
        background-color: white !important;
        position: relative !important;
        z-index: 20 !important; /* Updated to match header z-index */
    }

    /* SCOPED: Add pseudo-element border as ultimate fallback */
    .sanka-table table.dataTable thead tr::after,
    .sanka-table #contacts-table thead tr::after,
    .sanka-table .dataTables_scrollHead thead tr::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 22;
        pointer-events: none;
    }

    /* GLOBAL FALLBACK: Rules that work without sanka-table wrapper */
    /* Override JavaScript inline styles and ensure proper DataTables FixedColumns behavior */

    /* Remove JavaScript debugging borders and backgrounds */
    #contacts-table th:nth-child(1)[style*="background-color"],
    #contacts-table td:nth-child(1)[style*="background-color"],
    #contacts-table th:nth-child(2)[style*="background-color"],
    #contacts-table td:nth-child(2)[style*="background-color"] {
        background-color: white !important;
        border: none !important;
    }

    /* Ensure DataTables FixedColumns classes work properly */
    #contacts-table th.dtfc-fixed-left,
    #contacts-table td.dtfc-fixed-left {
        background-color: white !important;
        border-right: 1px solid #dee2e6 !important;
    }

    /* Specific styling for ID column (second column) */
    #contacts-table th.dtfc-fixed-left:nth-child(2),
    #contacts-table td.dtfc-fixed-left:nth-child(2) {
        border-right: 2px solid #dee2e6 !important;
    }

    /* Header specific styling */
    #contacts-table thead th.dtfc-fixed-left,
    #contacts-table thead td.dtfc-fixed-left {
        border-bottom: 2px solid #dee2e6 !important;
        z-index: 25 !important;
    }

    /* Ensure header borders are consistent */
    #contacts-table thead th,
    #contacts-table thead td {
        background-color: white !important;
    }

    /* Fallback: Ensure original thead is sticky when DataTables doesn't create scrollHead */
    #contacts-table thead.position-sticky {
        position: sticky !important;
        top: 0 !important;
        z-index: 20 !important;
        background-color: white !important;
    }

    /* But if DataTables creates scrollHead, let it handle the stickiness */
    #contacts-table_wrapper .dataTables_scrollHead ~ * #contacts-table thead.position-sticky {
        position: static !important;
    }

    /* CRITICAL: Ensure DataTables scroll head is sticky */
    #contacts-table_wrapper .dataTables_scrollHead,
    .sanka-table #contacts-table_wrapper .dataTables_scrollHead {
        position: sticky !important;
        top: 0 !important;
        z-index: 20 !important;
        background-color: white !important;
    }

    /* Ensure the inner scroll head container is also properly styled */
    #contacts-table_wrapper .dataTables_scrollHeadInner,
    .sanka-table #contacts-table_wrapper .dataTables_scrollHeadInner {
        background-color: white !important;
        border-bottom: 2px solid #dee2e6 !important;
    }

    /* Style the table within the scroll head */
    #contacts-table_wrapper .dataTables_scrollHead table,
    .sanka-table #contacts-table_wrapper .dataTables_scrollHead table {
        margin-bottom: 0 !important;
        background-color: white !important;
    }

    #contacts-table_wrapper .dataTables_scrollHead table thead tr,
    .sanka-table #contacts-table_wrapper .dataTables_scrollHead table thead tr {
        background-color: white !important;
    }

    /* Override any manual positioning that conflicts with FixedColumns */
    #contacts-table th:nth-child(1):not(.dtfc-fixed-left),
    #contacts-table td:nth-child(1):not(.dtfc-fixed-left),
    #contacts-table th:nth-child(2):not(.dtfc-fixed-left),
    #contacts-table td:nth-child(2):not(.dtfc-fixed-left) {
        position: static !important;
        left: auto !important;
    }

    /* Ensure DataTables wrapper allows proper scrolling */
    #contacts-table_wrapper {
        overflow: visible !important;
    }

    #contacts-table_wrapper .dataTables_scroll {
        overflow: visible !important;
    }

    #contacts-table_wrapper .dataTables_scrollBody {
        overflow-x: auto !important;
        overflow-y: auto !important;
    }

    /* Force proper z-index stacking for fixed columns */
    .dtfc-fixed-left {
        z-index: 15 !important;
        background-color: white !important;
    }

    .dtfc-fixed-left-top {
        z-index: 25 !important;
        background-color: white !important;
    }

    /* CRITICAL: Force proper FixedColumns behavior when plugin fails */
    /* This ensures the first two columns stay fixed during Y-axis scrolling */
    #contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(1),
    #contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(2) {
        position: sticky !important;
        background-color: white !important;
        z-index: 10 !important;
    }

    #contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(1) {
        left: 0 !important;
    }

    #contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(2) {
        left: 60px !important; /* Adjust based on checkbox column width */
        border-right: 2px solid #dee2e6 !important;
    }

    /* Force header cells to also be sticky */
    #contacts-table_wrapper .dataTables_scrollHead table thead tr th:nth-child(1),
    #contacts-table_wrapper .dataTables_scrollHead table thead tr th:nth-child(2) {
        position: sticky !important;
        background-color: white !important;
        z-index: 20 !important;
    }

    #contacts-table_wrapper .dataTables_scrollHead table thead tr th:nth-child(1) {
        left: 0 !important;
    }

    #contacts-table_wrapper .dataTables_scrollHead table thead tr th:nth-child(2) {
        left: 60px !important;
        border-right: 2px solid #dee2e6 !important;
    }

    /* SCOPED: Ensure sticky columns maintain position during both X and Y scrolling */
    .sanka-table .dataTables_scrollBody {
        position: relative !important;
    }

    .sanka-table .dataTables_scrollBody table {
        position: relative !important;
    }

    /* REMOVED: Force sticky positioning - let DataTables FixedColumns handle positioning */

    /* SCOPED: Ensure fixed column headers maintain borders with proper z-index */
    .sanka-table table.dataTable thead th.dtfc-fixed-left,
    .sanka-table table.dataTable thead td.dtfc-fixed-left,
    .sanka-table #contacts-table thead th.dtfc-fixed-left,
    .sanka-table #contacts-table thead td.dtfc-fixed-left,
    .sanka-table .table-content thead th.dtfc-fixed-left,
    .sanka-table .table-content thead td.dtfc-fixed-left {
        z-index: 25 !important; /* Updated to match header z-index hierarchy */
        border-bottom: 2px solid #dee2e6 !important;
        border-right: 1px solid #dee2e6 !important;
        background-color: white !important;
    }

    /* GLOBAL FIX: Ensure ALL dropdown menus appear above DataTables elements */
    /* This applies to dropdowns outside .sanka-table containers too */
    .dropdown-menu,
    .dropdown-menu.show,
    ul.dropdown-menu,
    ul.dropdown-menu.show {
        z-index: 1060 !important; /* Higher than Bootstrap modal backdrop (1050) */
    }

    /* Specific fix for view dropdown menus that appear outside table containers */
    .dropdown-menu[data-popper-placement],
    .dropdown-menu[style*="transform"] {
        z-index: 1070 !important; /* Even higher for positioned dropdowns */
    }

    /* REMOVED: Don't change table-responsive overflow - it breaks horizontal scrolling */

    /* Instead, ensure dropdown positioning works with proper z-index and portal-like behavior */
    .dropdown-menu.show[style*="position: absolute"] {
        z-index: 1070 !important;
    }

    /* Specific fix for Bootstrap dropdowns with Popper.js positioning */
    .dropdown-menu.show[data-popper-placement] {
        z-index: 1070 !important;
    }

    /* Fix for view dropdown menus specifically */
    .dropdown-menu.tw-max-w-\\[250px\\].show,
    .dropdown-menu.tw-max-w-\\[180px\\].show {
        z-index: 1070 !important;
    }

    /* Ensure view container allows dropdown overflow */
    #view-container {
        overflow: visible !important;
    }

    /* Ensure table navigation area allows dropdown overflow */
    .nav.nav-tabs.nav-line-tabs,
    .tw-flex.align-items-end.justify-content-between {
        overflow: visible !important;
    }

    /* But maintain horizontal scroll for the actual table */
    .sanka-table .dataTables_wrapper,
    .sanka-table .dataTables_scroll,
    .sanka-table .dataTables_scrollBody {
        overflow-x: auto !important;
    }

    /* Ensure dropdown container allows overflow */
    .nav-item {
        overflow: visible !important;
    }

    /* Ensure all parent containers of dropdown allow overflow */
    .sanka-table,
    .sanka-table > *,
    .w-100.px-0.sanka-table {
        overflow: visible !important;
    }

    /* But restore scroll behavior for table data */
    .sanka-table .dataTables_scrollBody {
        overflow: auto !important;
    }

    /* SPECIFIC: Ensure ID column header has proper border */
    .sanka-table table.dataTable thead th.dtfc-fixed-left:nth-child(2),
    .sanka-table table.dataTable thead td.dtfc-fixed-left:nth-child(2),
    .sanka-table #contacts-table thead th.dtfc-fixed-left:nth-child(2),
    .sanka-table #contacts-table thead td.dtfc-fixed-left:nth-child(2) {
        border-right: 2px solid #dee2e6 !important;
        border-bottom: 2px solid #dee2e6 !important;
    }

    /* SCOPED: Debug info CSS */
    .sanka-table .debug-fixed-columns {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        z-index: 9999;
        font-family: monospace;
        font-size: 12px;
    }
</style>

