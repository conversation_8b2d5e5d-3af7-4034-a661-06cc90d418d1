{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}


{% block content %}

<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    td.z-index-3 {
        z-index: 3!important;
    }
    
    .dataTables_scrollHead {
        position: sticky !important;
        z-index: 3;
        background-color: white;
    }
</style>

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        <div class="{% include "data/utility/table-button.html" %}">
            {% if permission|check_permission:'edit' %}
            <button id='view-sync-items-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 create-view-settings-button"
                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                hx-trigger="click"
                onclick="fillActionCaseIds(this),check_permission_action(event, 'edit')"
                hx-target="#manage-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </span>
            </button>
            {% endif %}

            <script>
                {% if open_drawer == 'action_drawer_history' %}
                    $(document).ready(function() {
                        setTimeout(function() {
                            document.getElementById('view-sync-items-action-drawer').click()
                            setTimeout(function() {
                                document.getElementById('history-tab').click()
                            }, 1000)
                        }, 1000)
                    })
                {% endif %}
                function fillActionCaseIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var objIds = checkedIds;
                    objIds = objIds.filter(id => id !== 'on');
                    // Now set the hx-vals attribute with the updated account IDs
                    elm.setAttribute('hx-vals', 'js:{"drawer_type":"deals", "section": "action_history", "page": "{{page}}","action_tab":"action", "case_ids":getSelectedCases()}');
                }
            </script>
        </div>
        
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button"
                hx-get="{% url 'case_load_drawer' %}"
                {% comment %} hx-vals='{"object_type": "customer_case", "view_id":"{{view_filter.view.id}}", "download_view":true, "import_export_type":"export"}'
                hx-get="{% url 'commerce_view_setting' %}" {% endcomment %}
                hx-vals='{"drawer_type":"case-view-export-import","view_id":"{{view_filter.view.id}}","page": "commerce_deals","import_export_type":"export"}'

                hx-target="#manage-view-settings-drawer"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>

            <button type="button" class="{% include "data/utility/import-button.html" %}"
                hx-get="{% url 'case_load_drawer' %}" 
                
                hx-vals='{"drawer_type":"case-view-export-import","view_id":"{{view_filter.view.id}}","page": "commerce_deals","import_export_type":"import"}'
                
                hx-target="#manage-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner" 
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
        </div>

        {% if permission|check_permission:'edit' %}
            <div class="btn-group mb-2 tw-h-[32px]">
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 create-view-settings-button view_form_trigger" type="button"
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals = '{"drawer_type":"deals", "view_id": "{{view_filter.view.id}}","module":"{{menu_key}}" ,"set_id": "{{set_id}}"}'
                    hx-target="#manage-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner" 
                    style="border-radius: 0.475rem 0 0 0.475rem;"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
            
                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>  

                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                 >
                     <span class="svg-icon svg-icon-4">
                            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"></path>
                            </svg>
                        </span>                
                    </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    {% for set in property_sets %}
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden create-view-settings-button" type="button"
                    
                            hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                            hx-vals = '{"drawer_type":"deals", "view_id": "{{view_filter.view.id}}", "set_id": "{{set.id}}"}'
                            hx-target="#manage-view-settings-drawer"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            hx-trigger="click"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>  
            </div>
        {% endif %}
    </div>
</div>

{% include "data/common/advance_search/advance-search-style.html" %}

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="w-100 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="view-container">

                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] create-view-settings-button mb-2 tw-w-[30px]"
                                style="height: 26px;"

                                hx-vals='{"drawer_type":"contacts-view-settings","page": "deals","type":"create_view"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                hx-target="#manage-view-settings-drawer"
                                hx-swap="innerHTML"
                                hx-on::before-request="document.getElementById('manage-view-settings-drawer').innerHTML = '';"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                hx-vals='{"module": "{{menu_key}}", "drawer_type":"contacts-view-settings","page": "deals", "view_id":"{{view_filter.view.id}}","type":"update_view"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-on::before-request="document.getElementById('manage-view-settings-drawer').innerHTML = '';"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                                
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% include 'data/projects/partial-view-menu.html' %}

                </div>


                <div class="d-flex w-50 justify-content-end pe-5" id="view-container-1">
                    <div class="justify-content-end">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-25px tw-rounded-lg"
                                        value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                        <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="view-header-container" class="tw-hidden w-100">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %} 
                        {% if permission|check_permission:'edit' %}
                            <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')" 
                                data-bs-toggle="modal" data-bs-target="#edit_bulk_modal"
                                >
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    編集
                                    {% else %}
                                    Edit
                                    {% endif %}
                                </span>
                            </button>
                
                            <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="deals-form">
                                {% if LANGUAGE_CODE == 'ja'%}
                                複製
                                {% else %}
                                Duplicate
                                {% endif %}
                            </button>
                        
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive 
                            {% endif %}
                        </button>
                        {% endif %}
    
                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('view-header-container').classList.add('d-none')
                                document.getElementById('deals-view-contianer').classList.remove('d-none')
                            })
                        </script>
                        <script>
                            function getSelectedCases() {
                                var selectedOrders = [];
                                var classNameElements = document.getElementsByClassName("deals-selection");
                                
                                if (classNameElements){
                                    classNameElements.forEach(function(classNameElement) {
                                        if (classNameElement.checked) {
                                            selectedOrders.push(classNameElement.value);
                                        }
                                    });  
                                }
                                return selectedOrders;
                            }
                        </script>
                    </div>
                    <div class="d-flex">
                        {% if permission|check_permission:'edit' %}
                        <button id='view-sync-items-action-drawer' type="button" class="ml-auto ms-auto {% include "data/utility/gray-header-button.html" %} create-view-settings-button me-2"
                            hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                            hx-trigger="click"
                            onclick="fillActionCaseIds(this),check_permission_action(event, 'edit')"
                            hx-target="#manage-view-settings-drawer"
                            hx-indicator=".loading-drawer-spinner,.view-form"
                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                    <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                </svg>
                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アクション
                                {% else %}
                                Action
                                {% endif %}
                            </span>
                        </button>
                        {% endif %}

                        <button type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                            
                            {% comment %} hx-get="{% url 'commerce_view_setting' %}" {% endcomment %}
                            hx-get="{% url 'case_load_drawer' %}"
                            hx-target="#manage-view-settings-drawer"
                            hx-vals = '{}'
                            onclick="fillCaseExportIds(this)"

                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>
                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                エクスポート
                                {% else %}
                                Export
                                {% endif %}
                            </span>
                        </button>
                        <script>
                            function fillCaseExportIds(elm) {
                                // Call your JavaScript function to generate the account IDs dynamically
                                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                var checkedIds = [];
                                checkboxes.forEach(function(checkbox) {
                                    if (checkbox.checked) {
                                        checkedIds.push(checkbox.value);
                                    }
                                });
                                console.log("checkboxes: ", checkboxes)
                                var orderIds = checkedIds;
                                orderIds = orderIds.filter(id => id !== 'on');
                                // Now set the hx-vals attribute with the updated account IDs
                                console.log("orderIds: ", orderIds)
                                elm.setAttribute('hx-vals','{"object_type": "customer_case", "view_id":"{{view_filter.view.id}}", "download_view":true, "import_export_type":"export", "checkbox":"' + orderIds + '", "drawer_type":"case-view-export-import"}')
                                
                            }
                        </script>

                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %} 
            </div>
        </div>
    </div>

    {% if permission|check_permission:'edit' %}
        <div id='modal-load'
            hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_filter.view.id|to_str}}", "page": "{{page}}", "target":"{{target}}", "menu_key": "{{menu_key}}"}'
            hx-get="{% url 'get_bulk_update_properties' %}" 
            hx-trigger='load'
            hx-target="this">
        </div>
    {% endif %}


    {% comment %} End of Views {% endcomment %}
    
    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
            {% if LANGUAGE_CODE == 'ja'%}
            このページの全ての案件が選択されました。 
            {% else %}
            All cases on this page are selected. 
            {% endif %}
             <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全ての案件を選択する
                {% else %}
                Select all cases
                {% endif %}

            </a>  
        </div>
    </div>
    
    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-lg-row-fluid">
            <form method="POST" id="deals-form">
                {% csrf_token %}

                <div class="pt-0 table-responsive">
                    {% if not config_view or config_view != 'kanban' %}
                    <table class="{% include "data/utility/table.html" %} cases-table">
                        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                            <tr class="align-middle">
                                {% for deals_column in deals_columns %}
                                    <th {% if deals_column == 'checkbox' %} 
                                        class="{% include "data/utility/column-checkbox-size.html" %}" 
                                    {% elif deals_column == 'deal_id' %}
                                        class="min-w-50px border-right-1"
                                    {% else %} 
                                        class="text-nowrap" 
                                    {% endif %}>
                                            {% if deals_column != 'checkbox' %}
                                                {% if deals_column|search_custom_field_object_deals:request %}
                                                    {% with channel_column=deals_column|search_custom_field_object_deals:request %}
                                                        {{channel_column.name|display_column_deals:request}}
                                                    {% endwith %}
                                                {% elif 'price_information' in deals_column %}
                                                    {% with channel_column=deals_column|split:'|' %}
                                                    {% with channel_name=channel_column.2|search_custom_field_object_deals:request %}
                                                        {% if channel_column.1 == 'total_price' %}
                                                            {{channel_name.name}} - {% if LANGUAGE_CODE == 'ja' %}合計金額{% else %}Total Price{% endif %}
                                                        {% else %}
                                                            {{channel_name.name}} - {% if LANGUAGE_CODE == 'ja' %}税抜合計価格{% else %}Total Price Without Tax{% endif %}
                                                        {% endif %}
                                                    {% endwith %}
                                                    {% endwith %}
                                                {% elif deals_column|split:'|'|length > 1 %}
                                                    {% with channel_column=deals_column|split:'|'|search_custom_field_object_order_customer:request %}
                                                        {{channel_column.name}}
                                                    {% endwith %}  
                                                {% else %}
                                                    {% with deals_column=deals_column|display_column_deals:request %}
                                                        {{deals_column}}
                                                    {% endwith %}
                                                {% endif %}
                                            {% endif %}
                                    </th>
                                    {% if deals_column == 'deal_id' %} 
                                    <th class="" style="width: 20px;">
                                    </th>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        </thead>

                        <tbody class="fs-6">
                            {% for deal in deals %}
                                    <tr id="row-{{deal.id}}" 
                                    hx-get="{% host_url 'case_row_detail' deal.id host 'app' %}" 
                                    hx-vals='{"view_id": "{{view_filter.view.id}}", "selected_case_id": "{{selected_case_id}}", "page": "{{page}}", "target":"{{target}}", "menu_key": "{{menu_key}}"}'
                                    hx-trigger="load" 
                                    hx-indicator=".row_load-{{deal.id}}">
                                        <td class="d-flex justify-content-center w-100">
                                            <style>
                                                /* Styles for the spinner */
                                                .row_load-{{deal.id}} {
                                                    display: none; /* Initially hidden */
                                                }
                                                .htmx-request .row_load-{{deal.id}},
                                                .htmx-request.row_load-{{deal.id}} {
                                                    display: inline-block; /* Display during htmx request */
                                                }
                                            </style>
                                            <!-- Spinner icon -->
                                            <span class="spinner-border spinner-border-lg text-secondary row_load-{{deal.id}}" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                        </tbody>
                    </table>
                    
                    {% elif config_view == 'kanban' %}
                        
                        {% if view_filter.choice_customfield %}
                            {% read_custom_field view_filter.choice_customfield "deals" request as customfieldname %}
                                {% with choice_values=customfieldname|read_choice_value_viewfilter:view_filter %}
                                
                                    <div id="deals_kanban" class="mt-5"></div>
                    
                                <script>
                                    
                                    //Merge the data
                                    var kanbandata = []
                                    {% for choice_value in choice_values %}
                                        kanbandata.push({
                                            'id': "{{choice_value.value}}",
                                            'title': "{{choice_value.label|display_column_KANBAN:request}}",
                                            'item':[]
                                        })
                                    {% endfor %}

                                    // Class definition
                                    var KTJKanbanDemoBasic = function() {
                                        // Private functions
                                        var exampleBasic = function() {
                                            var kanban = new jKanban({
                                                element: '#deals_kanban',
                                                gutter: '0',
                                                widthBoard: '250px',
                                                dragBoards: false,   
                                                boards: kanbandata,
                                                dragEl: function(el, source) {
                                                    // Check if the dragged item is selected (has checkbox checked)
                                                    var draggedItemId = el.querySelector('span').id;
                                                    var draggedCheckbox = el.querySelector('input[type="checkbox"]');
                                                    var selectedItems = [];
                                                    
                                                    // If the dragged item is not selected, select it
                                                    if (!draggedCheckbox || !draggedCheckbox.checked) {
                                                        if (draggedCheckbox) {
                                                            draggedCheckbox.checked = true;
                                                            updateKanbanSelectionUI();
                                                        }
                                                    }
                                                    
                                                    // Get all selected items for multi-drag
                                                    var allCheckboxes = document.querySelectorAll('.kanban-item input[type="checkbox"]:checked');
                                                    allCheckboxes.forEach(function(checkbox) {
                                                        var itemElement = checkbox.closest('.kanban-item');
                                                        if (itemElement) {
                                                            selectedItems.push({
                                                                id: itemElement.querySelector('span').id,
                                                                element: itemElement
                                                            });
                                                        }
                                                    });
                                                    
                                                    // Store selected items for use in dropEl
                                                    window.kanbanSelectedItems = selectedItems;
                                                    
                                                    // Handle scrolling based on element position
                                                    var container = document.querySelector('.flex-lg-row-fluid');
                                                    var containerRect = container.getBoundingClientRect();
                                                    
                                                    // Add mousemove event listener during drag
                                                    function handleMouseMove(e) {
                                                        var threshold = 100; // pixels from edge to trigger scroll
                                                        console.log("handleMouseMove")
                                                        // If mouse is near right edge
                                                        if (e.clientX > containerRect.right - threshold) {
                                                            document.querySelector('.kanban-container').scrollBy({
                                                                left: 100,
                                                                behavior: 'smooth'
                                                            });
                                                        }
                                                        
                                                        // If mouse is near left edge
                                                        if (e.clientX < containerRect.left + threshold) {
                                                            document.querySelector('.kanban-container').scrollBy({
                                                                left: -100,
                                                                behavior: 'smooth'
                                                            });
                                                        }

                                                        
                                                        // Get the currently visible viewport height
                                                        var viewportHeight = window.innerHeight;
                                                        // Vertical scrolling
                                                        // For bottom edge - trigger when mouse is near bottom of viewport
                                                        if (e.clientY > viewportHeight - threshold) {
                                                            document.querySelector('#kt_content_container').scrollBy({
                                                                top: 1000,
                                                                behavior: 'smooth'
                                                            });
                                                        }
                                                        
                                                        // For top edge - trigger when mouse is near top of viewport
                                                        if (e.clientY < threshold) {
                                                            document.querySelector('#kt_content_container').scrollBy({
                                                                top: -1000,
                                                                behavior: 'smooth'
                                                            });
                                                        }
                                                        
                                                    }

                                                    // Add listener when drag starts
                                                    document.addEventListener('mousemove', handleMouseMove);

                                                    // Clean up listener when drag ends
                                                    document.addEventListener('mouseup', function cleanup() {
                                                        document.removeEventListener('mousemove', handleMouseMove);
                                                        document.removeEventListener('mouseup', cleanup);
                                                    });
                                                },
                                                dragendBoard: function (el) {
                                                    var boardIds = $('.kanban-board').map(function(){
                                                        return $(this).attr("data-id")
                                                    }).get();
                            
                                                    var sorted = [];
                                                    var currentOrder = 0;
                                                    boardIds.forEach(function(value, index, array) {
                                                        sorted.push({
                                                            "id": value,
                                                            "order": currentOrder++
                                                        })
                                                    });
                                                    
                                                    var target_status = JSON.stringify(sorted)
                                                    // Send Ajax request
                                                    // URL of the API endpoint you want to fetch data from
                                                    const apiUrl = "{% host_url 'kanban_board_api' host 'app' %}";
                                                    const postData = {
                                                        type: 'case',
                                                        target_status:target_status,
                                                        view_id:"{{view_filter.id}}"
                                                        };
                                                    // Configuring the fetch options for a POST request
                                                    const fetchOptions = {
                                                        method: 'POST',
                                                        headers: {
                                                        'Content-Type': 'application/json',
                                                        'X-CSRFToken': "{{ csrf_token }}",
                                                        },
                                                        body: JSON.stringify(postData),
                                                    };
                                                    // Using the Fetch API to make a GET request
                                                    fetch(apiUrl,fetchOptions)
                                                        .then(response => {
                                                        // Check if the request was successful (status code 200-299)
                                                        if (!response.ok) {
                                                            throw new Error(`HTTP error! Status: ${response.status}`);
                                                        }
    
                                                        // Parse the response as JSON
                                                        return response.json();
                                                        })
                                                        .then(data => {
                                                        // Handle the data received from the API
                                                        // console.log('Data received:', data);
                                                        })
                                                        .catch(error => {
                                                        // Handle errors that may occur during the fetch operation
                                                        console.error('Fetch error:', error);
                                                        });
    
    
    
                                                },
                                                dropEl: function (el, target, source, sibling) {
                                                    var target_status = target.parentElement.getAttribute('data-id');
                                                    var selectedItems = window.kanbanSelectedItems || [];
                                                    
                                                    // If no selected items, fall back to single item
                                                    if (selectedItems.length === 0) {
                                                        selectedItems = [{
                                                            id: el.querySelector('span').id,
                                                            element: el
                                                        }];
                                                    }
                                                    
                                                    // Process each selected item
                                                    selectedItems.forEach(function(item, index) {
                                                        // Skip the dragged item as it's already moved by jKanban
                                                        if (item.element === el) return;
                                                        
                                                        // Move other selected items to the same target
                                                        var itemElement = item.element;
                                                        if (itemElement && itemElement.parentNode !== target) {
                                                            // Remove from source
                                                            itemElement.parentNode.removeChild(itemElement);
                                                            // Add to target (after the dragged item)
                                                            if (sibling) {
                                                                target.insertBefore(itemElement, sibling.nextSibling);
                                                            } else {
                                                                target.appendChild(itemElement);
                                                            }
                                                        }
                                                    });
                                                    
                                                    // Get all items in the target column after moving
                                                    var items = target.querySelectorAll('.kanban-item');
                                                    var newOrder = Array.from(items).map(item => item.querySelector('span').id);
                                                    
                                                    // Prepare data for all selected items
                                                    var itemIds = selectedItems.map(item => item.id);
                                                    
                                                    // Send Ajax request for all selected items
                                                    const apiUrl = "{% host_url 'kanban_status_api' host 'app' %}";
                                                    const postData = {
                                                        id: itemIds.length === 1 ? itemIds[0] : itemIds,
                                                        type: 'deals',
                                                        view_id: '{{view_filter.view.id}}',
                                                        target_status: target_status,
                                                        newOrder: newOrder,
                                                        multi_drag: itemIds.length > 1
                                                    };
                                                    
                                                    // Configuring the fetch options for a POST request
                                                    const fetchOptions = {
                                                        method: 'POST',
                                                        headers: {
                                                            'Content-Type': 'application/json',
                                                            'X-CSRFToken': "{{ csrf_token }}",
                                                        },
                                                        body: JSON.stringify(postData),
                                                    };
                                                    
                                                    // Using the Fetch API to make a POST request
                                                    fetch(apiUrl, fetchOptions)
                                                        .then(response => {
                                                            // Check if the request was successful (status code 200-299)
                                                            if (!response.ok) {
                                                                throw new Error(`HTTP error! Status: ${response.status}`);
                                                            }
                                                            // Parse the response as JSON
                                                            return response.json();
                                                        })
                                                        .then(data => {
                                                            // Handle the data received from the API
                                                            console.log('Multi-drag completed:', data);
                                                            // Clear selected items after successful drop
                                                            window.kanbanSelectedItems = [];
                                                            // Uncheck all checkboxes
                                                            document.querySelectorAll('.kanban-item input[type="checkbox"]').forEach(function(checkbox) {
                                                                checkbox.checked = false;
                                                            });
                                                            updateKanbanSelectionUI();
                                                        })
                                                        .catch(error => {
                                                            // Handle errors that may occur during the fetch operation
                                                            console.error('Fetch error:', error);
                                                        });
                                                },
                                                
                                            });

                                            // Add "Load More" button to each board's container
                                            kanban.options.boards.forEach(function(board) {
                                                console.log("board.id: ", board.id)
                                                addLoadMoreButtonToBoard(kanban, board.id);
                                            });
                                            
                                        };

                                        

                                        var currentPage={};
                                        // Function to add "Load More" button directly inside the board container
                                        function addLoadMoreButtonToBoard(kanban, boardStatus) {
                                            var boardIDAttr = '[data-id="' + boardStatus + '"]'
                                            var boardElement = document.querySelector(boardIDAttr);
                                            boardElement.style.display = 'flex';
                                            boardElement.style.flexDirection = 'column';
                                            boardElement.style.height = '100%'; // Make sure the board takes full height
                                            boardElement.style.justifyContent = 'space-between'; // Distribute items to keep the button at the bottom
                                            boardElement.style.minHeight = '0%'
                                            
                                            currentPage[boardStatus] = 1;  // Initialize the page number for this board

                                            var boardItemStr=`
                                            <div id="container-load-more-btn-${boardStatus}" style="display: flex; justify-content: center; margin-top: auto;">
                                                <button id="load-more-btn-${boardStatus}" 
                                                        style="width: 100%; padding: 5px; background-color: #472CF5; color: white; border: none; border-radius: 4px; text-align: center; font-weight: bold;"
                                                        hx-get="${window.location.href}" 
                                                        hx-vals='{ "boardStatus": "${boardStatus}", "page": "${currentPage[boardStatus]}" }' 
                                                        hx-target="[data-id='${boardStatus}'] .kanban-drag"
                                                        hx-swap="beforeend" ,
                                                        hx-trigger="load,click"
                                                        >
                                                    
                                                {% if LANGUAGE_CODE == 'ja'%}もっと見る{% else %}Load More{% endif %}

                                                </button>
                                            </div>`

                                            boardElement.insertAdjacentHTML('beforeend', boardItemStr);

                                            // Use HTMX event to update the page number after the content is loaded
                                            document.getElementById('load-more-btn-' + boardStatus).addEventListener('htmx:afterRequest', function() {
                                                var responseHTML = event.detail.xhr.responseText;
                                                if (!responseHTML.trim()) {
                                                    this.style.display = 'none';
                                                } else {
                                                    currentPage[boardStatus]++;  // Increment the page number for this specific board after the request is made
                                                    this.setAttribute('hx-vals', JSON.stringify({ boardStatus: boardStatus, page: currentPage[boardStatus] }));
                                                    
                                                    // Check if there are more pages to load
                                                    var parser = new DOMParser();
                                                    var doc = parser.parseFromString(responseHTML, 'text/html');
                                                    var kanbanItems = doc.getElementsByClassName('kanban-' + boardStatus);
                                                    var lastKanbanItem = kanbanItems[kanbanItems.length - 1];
                                                    var hasNext = lastKanbanItem ? lastKanbanItem.getAttribute('data-has-next') : 'false';
                                                    if (hasNext === 'false') {
                                                        this.style.display = 'none';
                                                    }
                                                
                                                }
                                            });
                                        }


                                        return {
                                            // Public Functions
                                            init: function() {
                                                exampleBasic();
                                            }
                                        };
                                    }();

                                    KTJKanbanDemoBasic.init();

                                    {% if not view_filter.kanban_unlisted %}
                                        var board = document.querySelector('div[data-id="unlisted"]');
                                        // Example: Hide the board
                                        if (board) {
                                            board.style.display = 'none';
                                        }
                                    {% endif %}
                                    
                                    // Function to update kanban selection UI
                                    function updateKanbanSelectionUI() {
                                        var checkedBoxes = document.querySelectorAll('.kanban-item input[type="checkbox"]:checked');
                                        var headerContainer = document.getElementById('view-header-container');
                                        
                                        if (checkedBoxes.length > 0) {
                                            headerContainer.classList.remove('tw-hidden');
                                            headerContainer.classList.remove('d-none');
                                        } else {
                                            headerContainer.classList.add('tw-hidden');
                                            headerContainer.classList.add('d-none');
                                        }
                                    }
                                    
                                    // Function to get selected cases from kanban
                                    function getSelectedCases() {
                                        var selectedOrders = [];
                                        
                                        // Check table view checkboxes
                                        var tableCheckboxes = document.getElementsByClassName("deals-selection");
                                        if (tableCheckboxes && tableCheckboxes.length > 0) {
                                            Array.from(tableCheckboxes).forEach(function(checkbox) {
                                                if (checkbox.checked) {
                                                    selectedOrders.push(checkbox.value);
                                                }
                                            });
                                        }
                                        
                                        // Check kanban view checkboxes
                                        var kanbanCheckboxes = document.querySelectorAll('.kanban-item input[type="checkbox"]:checked');
                                        kanbanCheckboxes.forEach(function(checkbox) {
                                            selectedOrders.push(checkbox.value);
                                        });
                                        
                                        return selectedOrders;
                                    }
                                    
                                    // Add event listeners for kanban checkboxes when items are loaded
                                    document.addEventListener('htmx:afterRequest', function(event) {
                                        // Add event listeners to newly loaded kanban items
                                        var newCheckboxes = event.target.querySelectorAll('.kanban-item input[type="checkbox"]');
                                        newCheckboxes.forEach(function(checkbox) {
                                            checkbox.addEventListener('change', function() {
                                                updateKanbanSelectionUI();
                                            });
                                        });
                                    });
                                    
                                    // Initial setup for existing checkboxes
                                    document.addEventListener('DOMContentLoaded', function() {
                                        setTimeout(function() {
                                            var existingCheckboxes = document.querySelectorAll('.kanban-item input[type="checkbox"]');
                                            existingCheckboxes.forEach(function(checkbox) {
                                                checkbox.addEventListener('change', function() {
                                                    updateKanbanSelectionUI();
                                                });
                                            });
                                        }, 1000);
                                    });
                                    
                                </script>

                                <style>
                                    .kanban-container{
                                        flex-wrap: nowrap !important;
                                        overflow-x: auto !important;
                                    }
                                    
                                    /* Kanban checkbox styles */
                                    .kanban-item {
                                        position: relative;
                                    }
                                    
                                    .kanban-item input[type="checkbox"] {
                                        position: absolute;
                                        top: 8px;
                                        left: 8px;
                                        z-index: 10;
                                        width: 16px;
                                        height: 16px;
                                        cursor: pointer;
                                    }
                                    
                                    .kanban-item:hover input[type="checkbox"] {
                                        opacity: 1;
                                    }
                                    
                                    .kanban-item input[type="checkbox"]:checked {
                                        opacity: 1;
                                    }
                                    
                                    .kanban-item input[type="checkbox"]:not(:checked) {
                                        opacity: 0.3;
                                    }
                                    
                                    /* Selected item styling */
                                    .kanban-item:has(input[type="checkbox"]:checked) {
                                        border: 2px solid #007bff;
                                        background-color: #f8f9ff;
                                    }
                                    
                                    /* Ensure content doesn't overlap with checkbox */
                                    .kanban-item > div:first-child {
                                        padding-left: 30px;
                                    }
                                </style>

                                {% endwith %}
                                
                            
                            {% else %}
                            <h5 class="mt-4 card-title fw-bold">
                                {% if LANGUAGE_CODE == 'ja'%}
                                カンバンステータスが設定されていません。表示設定でカンバンステータスを確認してください。
                                {% else %}
                                Kanban Status UNSET. Please Check Kanban Status on View Setting.
                                {% endif %}
                            </h5>
                            {% endif %}   
    
                    {% endif %}


                    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}

                    <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括アーカイブの確認
                                            {% else %}
                                            Bulk Archive Confirmation
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    選択されたレコードをアーカイブしてもよろしいですか?
                                                    {% else %}
                                                    Are you sure to archive selected records?
                                                    {% endif %}
                                                </span>
                                            </label>
                                    
                                        </div>
                                    </div>
                                </div>

                
                                <div class="modal-footer border-0">
                                    <button name="bulk_delete_deals" type="submit" class="btn btn-danger">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        アーカイブ
                                        {% else %}
                                        Archive
                                        {% endif %}
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> 

                    <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括有効化の確認
                                            {% else %}
                                            Bulk Activate Confirmation
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    これらの案件を有効化してもよろしいですか?
                                                    {% else %}
                                                    Are you sure to activate these Deals?
                                                    {% endif %}
                                                </span>
                                            </label>
                                    
                                        </div>
                                    </div>
                                </div>
                                
                
                                <div class="modal-footer border-0">
                                    <button name="bulk_restore_deals" type="submit" class="btn btn-success">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        有効化
                                        {% else %}
                                        Activate
                                        {% endif %}
                                        
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> 


                </div>
            </form>
            

            {% if config_view != 'kanban' %}
            <div class="{% include "data/utility/pagination.html" %}">
                {% if LANGUAGE_CODE == 'ja'%}
                {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                {% else %}
                Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                {% endif %}

                <div>
                    
                    {% if page_content.has_previous %}     
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% if LANGUAGE_CODE == 'ja'%}最初 {% else %}First{% endif %}</a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}前 {% else %}Previous{% endif %}</a>
                    {% endif %}
                            
                    {% if page_content.has_next %}
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}次 {% else %}Next{% endif %}</a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}最後 {% else %}Last{% endif %} &raquo;</a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

        </div>
    </div>

</div>




{% if case_id %}
<a id="selected_show_case" class="d-none shopturbo-manage-wizard-button"
    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
    hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{case_id}}" ,"view_id":"{{view_id}}"}'
    hx-target="#manage-view-settings-drawer"
    hx-indicator=".loading-drawer-spinner" 
    hx-trigger="click"></a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_case').click();
        }, 1);
    });
</script>
{% endif %}

{% endif %}
{% endblock %}

{% block js %}

<script>
    // Function to load scripts dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        document.head.appendChild(script);
    }

    // Function to ensure DataTables is loaded before initialization
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'function') {
            callback();
        } else {
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    callback();
                });
            });
        }
    }

    {% if deals %}
    var requestNum = {{deals|length}}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum -= 1;
            if (requestNum <= 0) {
                // Make sure jQuery and DataTables are properly initialized
                ensureDataTableScripts(function() {
                    var table = $(".cases-table").DataTable({
                        scrollX: true,
                        scrollY: "75vh",
                        scrollCollapse: true,
                        fixedColumns:   {
                            left: 2
                        },
                        ordering: false,
                        searching: false,  // Hide the search bar
                        paging: false,      // Hide pagination
                        info: false,        // Hide the information text
                        language: {
                            emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                        }
                    });

                    const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                    const dropdown = dropdowns.each((index, dropdownToggleEl) => {
                        var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                            popperConfig(defaultBsPopperConfig) {
                                return { ...defaultBsPopperConfig, strategy: "fixed" };
                            },
                        });

                        dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                            $(event.target).closest("td").addClass("z-index-3");
                        });

                        dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                            $(event.target).closest("td").removeClass("z-index-3");
                        });
                    });

                    {% include "data/common/open-drawer.js" %}
                });
            }
        }
    });
    {% else %}
    // Make sure jQuery and DataTables are properly initialized
    ensureDataTableScripts(function() {
        $(".cases-table").DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            fixedColumns:   {
                left: 2
            },
            ordering: false,
            searching: false,  // Hide the search bar
            paging: false,      // Hide pagination
            info: false,        // Hide the information text
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            }
        });
    });
    {% endif %}

    

    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-contact");
			addcontactelem.classList.add("disabled");
            downloadelem = document.getElementById("csv_download-contact");
			downloadelem.classList.add("disabled");
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "Select All {{paginator.count}} contacts in this sections";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    {% comment %} {% include "data/common/open-drawer.js" %} {% endcomment %}

    
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% include 'data/javascript/toggleSearch.html' %} 

{% endblock %}
